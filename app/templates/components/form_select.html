

{% macro form_select(
  id,
  name,
  options,
  selected_value=None,
  placeholder="Select an option",
  left_icon=None,
  right_icon="chevron-down",
  required=False,
  disabled=False,
  classes="",
  data_attributes={}
) %}
<div class="relative isolate overflow-visible">
  <select
    id="{{ id }}"
    name="{{ name }}"
    class="flex h-9 w-full rounded-md border border-gray-300 dark:border-gray-800 bg-white dark:bg-slate-900 text-gray-700 dark:text-gray-200 px-3 py-1 {% if left_icon %}pl-9{% endif %} {% if right_icon %}pr-9{% endif %} text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 dark:placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-blue-500 dark:focus-visible:ring-blue-400 focus-visible:border-blue-500 dark:focus-visible:border-blue-400 hover:border-gray-400 dark:hover:border-gray-500 disabled:cursor-not-allowed disabled:opacity-50 appearance-none select-none bg-none z-10 {{ classes }}"
    {% if required %}required{% endif %}
    {% if disabled %}disabled{% endif %}
    {% for key, value in data_attributes.items() %}
      data-{{ key }}="{{ value }}"
    {% endfor %}
  >
    {% if placeholder %}
      <option value="">{{ placeholder }}</option>
    {% endif %}

    {% for option in options %}
      {% if option is mapping %}
        <option
          value="{{ option.value }}"
          {% if selected_value is not none and selected_value == option.value %}selected{% endif %}
          {% if option.disabled %}disabled{% endif %}
        >
          {{ option.label }}
        </option>
      {% else %}
        <option
          value="{{ option }}"
          {% if selected_value is not none and selected_value == option %}selected{% endif %}
        >
          {{ option }}
        </option>
      {% endif %}
    {% endfor %}
  </select>

  {% if left_icon %}
  <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none z-20 select-none">
    <i data-lucide="{{ left_icon }}" class="h-4 w-4 text-gray-500 dark:text-gray-400 pointer-events-none"></i>
  </div>
  {% endif %}

  {% if right_icon %}
  <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-20 select-none">
    <i data-lucide="{{ right_icon }}" class="h-4 w-4 text-gray-500 dark:text-gray-400 pointer-events-none"></i>
  </div>
  {% endif %}
</div>
{% endmacro %}

{% macro form_select_with_label(
  id,
  name,
  label,
  options,
  selected_value=None,
  placeholder="Select an option",
  left_icon=None,
  right_icon="chevron-down",
  label_icon=None,
  required=False,
  disabled=False,
  classes="",
  data_attributes={}
) %}
<div class="grid gap-1.5">
  <label for="{{ id }}" class="text-sm font-medium leading-none text-gray-700 dark:text-gray-300 peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center">
    {% if label_icon %}
    <i data-lucide="{{ label_icon }}" class="h-3.5 w-3.5 mr-1.5 text-gray-500 dark:text-gray-400"></i>
    {% endif %}
    {{ label }}
  </label>
  {{ form_select(
    id=id,
    name=name,
    options=options,
    selected_value=selected_value,
    placeholder=placeholder,
    left_icon=left_icon,
    right_icon=right_icon,
    required=required,
    disabled=disabled,
    classes=classes,
    data_attributes=data_attributes
  ) }}
</div>
{% endmacro %}
