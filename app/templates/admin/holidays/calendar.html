{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/button.html" import button, button_group, icon_button %}

{% block title %}Holiday Calendar{% endblock %}

{% block header %}Holiday Calendar{% endblock %}

{% block content %}
<!-- Hidden fields for JavaScript -->
<input type="hidden" id="calendar-api-url" data-url="{{ url_for('api.get_holiday_calendar', region_code='PH', year=2025) }}">
<input type="hidden" id="quick-add-url" data-url="{{ url_for('admin.create_holiday') }}">
<input type="hidden" id="csrf-token" data-token="{{ csrf_token() }}">

<div class="space-y-6">
  {{ page_header(
    title="Holiday Calendar",
    button_text="Add Holiday",
    button_icon="plus",
    button_action="openAddHolidayForm()",
    description="View and manage holidays in calendar format."
  ) }}

  <!-- Calendar Controls -->
  <div class="card">
    <div class="card-content">
      <div class="flex flex-wrap items-center justify-between gap-4">
        <!-- View Controls -->
        <div class="flex items-center space-x-4">
          <!-- Region Selector -->
          <div class="flex items-center space-x-2">
            <label for="regionSelect" class="text-sm font-medium">Region:</label>
            <select id="regionSelect" onchange="updateCalendar()" class="input w-auto">
              {% for region in available_regions %}
              <option value="{{ region }}" {% if region == current_region %}selected{% endif %}>
                {% if region == 'US' %}🇺🇸 United States
                {% elif region == 'PH' %}🇵🇭 Philippines
                {% elif region == 'GLOBAL' %}🌍 Global
                {% else %}{{ region }}{% endif %}
              </option>
              {% endfor %}
            </select>
          </div>

          <!-- Year Selector -->
          <div class="flex items-center space-x-2">
            <label for="yearSelect" class="text-sm font-medium">Year:</label>
            <select id="yearSelect" onchange="updateCalendar()" class="input w-auto">
              {% for year in range(current_year - 2, current_year + 3) %}
              <option value="{{ year }}" {% if year == current_year %}selected{% endif %}>{{ year }}</option>
              {% endfor %}
            </select>
          </div>

          <!-- Month Selector -->
          <div class="flex items-center space-x-2">
            <label for="monthSelect" class="text-sm font-medium">Month:</label>
            <select id="monthSelect" onchange="updateCalendar()" class="input w-auto">
              <option value="0">All Months</option>
              <option value="1" {% if current_month == 1 %}selected{% endif %}>January</option>
              <option value="2" {% if current_month == 2 %}selected{% endif %}>February</option>
              <option value="3" {% if current_month == 3 %}selected{% endif %}>March</option>
              <option value="4" {% if current_month == 4 %}selected{% endif %}>April</option>
              <option value="5" {% if current_month == 5 %}selected{% endif %}>May</option>
              <option value="6" {% if current_month == 6 %}selected{% endif %}>June</option>
              <option value="7" {% if current_month == 7 %}selected{% endif %}>July</option>
              <option value="8" {% if current_month == 8 %}selected{% endif %}>August</option>
              <option value="9" {% if current_month == 9 %}selected{% endif %}>September</option>
              <option value="10" {% if current_month == 10 %}selected{% endif %}>October</option>
              <option value="11" {% if current_month == 11 %}selected{% endif %}>November</option>
              <option value="12" {% if current_month == 12 %}selected{% endif %}>December</option>
            </select>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-2">
          {{ button("List View", variant="outline", href=url_for('admin.holidays'), icon="list") }}
          {{ icon_button("refresh-cw", variant="outline", onclick="refreshCalendar()", title="Refresh Calendar") }}
        </div>
      </div>
    </div>
  </div>

  <!-- Calendar Display -->
  <div class="card">
    <div class="card-header">
      <div class="flex items-center justify-between">
        <div>
          <h3 id="calendarTitle" class="card-title">
            {{ calendar_data.year }}
            {% if calendar_data.month %}
            - {{ ['', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'][calendar_data.month] }}
            {% endif %}
            ({{ calendar_data.region_code }})
          </h3>
          <div class="text-sm text-muted-foreground">
            {{ calendar_data.total_holidays }} holidays found
          </div>
        </div>

        <!-- Navigation Controls -->
        <div class="flex items-center">
          {% set nav_buttons = [
            {"text": "Previous", "variant": "outline", "size": "sm", "onclick": "navigateCalendar('prev')", "icon": "chevron-left"},
            {"text": "Today", "variant": "outline", "size": "sm", "onclick": "navigateCalendar('today')", "icon": "calendar"},
            {"text": "Next", "variant": "outline", "size": "sm", "onclick": "navigateCalendar('next')", "icon": "chevron-right", "icon_position": "right"}
          ] %}
          {{ button_group(nav_buttons) }}
        </div>
      </div>
    </div>
    <div class="card-content">
      <div id="calendarContainer">
        <!-- Calendar will be rendered here by JavaScript -->
        <div class="text-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p class="text-muted-foreground mt-2">Loading calendar...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Holiday Summary -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Upcoming Holidays -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Upcoming Holidays</h3>
      </div>
      <div class="card-content">
        <div id="upcomingHolidays" class="space-y-3">
          <!-- Will be populated by JavaScript -->
        </div>
      </div>
    </div>

    <!-- Holiday Statistics -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Statistics</h3>
      </div>
      <div class="card-content">
        <div id="holidayStats" class="space-y-3">
          <!-- Will be populated by JavaScript -->
        </div>
      </div>
    </div>

    <!-- Legend -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Legend</h3>
      </div>
      <div class="card-content">
        <div class="space-y-2">
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 bg-blue-500 rounded"></div>
            <span class="text-sm">🇺🇸 US Holidays</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 bg-green-500 rounded"></div>
            <span class="text-sm">🇵🇭 PH Holidays</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 bg-gray-500 rounded"></div>
            <span class="text-sm">🌍 Global Holidays</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 bg-purple-500 rounded"></div>
            <span class="text-sm">📅 Today</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


{% endblock %}

{% block scripts %}
{{ super() }}
<script src="{{ url_for('static', filename='js/pages/holiday-calendar.js') }}"></script>
{% endblock %}
