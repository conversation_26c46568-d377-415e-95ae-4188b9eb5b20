/**
 * Holiday Calendar JavaScript
 * Handles calendar view, navigation, and holiday display
 */

// Global variables
let currentCalendarData = null;
let currentYear = new Date().getFullYear();
let currentMonth = 0; // 0 = all months
let currentRegion = 'PH';

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeHolidayCalendar();
    registerHolidayForms();
});

/**
 * Register holiday forms with drawer manager
 */
function registerHolidayForms() {
    // Check if drawerManager exists
    if (!window.drawerManager) {
        console.error('DrawerManager not found');
        return;
    }

    // Register holiday form
    window.drawerManager.registerFormType('holiday', {
        createUrl: '/admin/holidays/form',
        editUrl: '/admin/holidays/form/{id}',
        size: 'md',
        position: 'right'
    });

    console.log('Holiday forms registered with drawer manager');
}

/**
 * Initialize holiday calendar functionality
 */
function initializeHolidayCalendar() {
    // Get initial values from selectors
    currentYear = parseInt(document.getElementById('yearSelect').value);
    currentMonth = parseInt(document.getElementById('monthSelect').value);
    currentRegion = document.getElementById('regionSelect').value;

    // Load initial calendar data
    updateCalendar();

    console.log('Holiday calendar initialized');
}



/**
 * Update calendar display
 */
async function updateCalendar() {
    try {
        showCalendarLoading();

        // Get calendar data from API
        const calendarData = await fetchCalendarData();
        currentCalendarData = calendarData;

        // Render calendar
        renderCalendar(calendarData);

        // Update title
        updateCalendarTitle();

        // Update sidebar components
        updateUpcomingHolidays();
        updateHolidayStats();

    } catch (error) {
        console.error('Error updating calendar:', error);
        showCalendarError('Failed to load calendar data');
    }
}

/**
 * Fetch calendar data from API
 */
async function fetchCalendarData() {
    const apiUrl = `/api/holidays/calendar/${currentRegion}/${currentYear}`;
    const params = new URLSearchParams();

    if (currentMonth > 0) {
        params.append('month', currentMonth);
    }

    const url = params.toString() ? `${apiUrl}?${params}` : apiUrl;

    const response = await fetch(url);

    if (!response.ok) {
        throw new Error('Failed to fetch calendar data');
    }

    const data = await response.json();

    if (!data.success) {
        throw new Error(data.error || 'Failed to fetch calendar data');
    }

    return data.calendar_data;
}

/**
 * Render calendar display
 */
function renderCalendar(calendarData) {
    const container = document.getElementById('calendarContainer');

    if (currentMonth === 0) {
        // Show year view (all months)
        renderYearView(container, calendarData);
    } else {
        // Show month view
        renderMonthView(container, calendarData);
    }
}

/**
 * Render year view (all months)
 */
function renderYearView(container, calendarData) {
    const months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];

    let html = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">';

    months.forEach((monthName, index) => {
        const monthNumber = index + 1;
        const monthHolidays = getHolidaysForMonth(calendarData.holidays, monthNumber);

        html += `
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title text-sm">${monthName} ${calendarData.year}</h4>
                    <span class="text-xs text-muted-foreground">${monthHolidays.length} holidays</span>
                </div>
                <div class="card-content">
                    ${renderMonthHolidays(monthHolidays)}
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

/**
 * Render month view (detailed calendar)
 */
function renderMonthView(container, calendarData) {
    const monthName = [
        '', 'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ][currentMonth];

    const monthHolidays = getHolidaysForMonth(calendarData.holidays, currentMonth);

    let html = `
        <div class="space-y-4">
            <div class="text-center">
                <h3 class="text-lg font-semibold">${monthName} ${calendarData.year}</h3>
                <p class="text-muted-foreground">${monthHolidays.length} holidays in this month</p>
            </div>

            <div class="grid gap-3">
                ${renderMonthHolidays(monthHolidays, true)}
            </div>
        </div>
    `;

    container.innerHTML = html;
}

/**
 * Get holidays for a specific month
 */
function getHolidaysForMonth(holidays, month) {
    const monthHolidays = [];

    Object.entries(holidays).forEach(([dateStr, holidayList]) => {
        const date = new Date(dateStr);
        if (date.getMonth() + 1 === month) {
            holidayList.forEach(holiday => {
                monthHolidays.push({
                    ...holiday,
                    date: dateStr,
                    dateObj: date
                });
            });
        }
    });

    return monthHolidays.sort((a, b) => a.dateObj - b.dateObj);
}

/**
 * Render holidays for a month
 */
function renderMonthHolidays(holidays, detailed = false) {
    if (holidays.length === 0) {
        return '<p class="text-muted-foreground text-sm text-center py-4">No holidays this month</p>';
    }

    let html = '';

    holidays.forEach(holiday => {
        const date = new Date(holiday.date);
        const isToday = isDateToday(date);
        const regionColor = getRegionColor(holiday.region_code);
        const regionFlag = getRegionFlag(holiday.region_code);

        if (detailed) {
            html += `
                <div class="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/30 cursor-pointer"
                     onclick="showHolidayDetails(${holiday.id})">
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 rounded-full ${regionColor}"></div>
                        <div>
                            <div class="font-medium">${holiday.name}</div>
                            <div class="text-sm text-muted-foreground">
                                ${regionFlag} ${formatDate(date)} ${isToday ? '(Today)' : ''}
                            </div>
                        </div>
                    </div>
                    <i data-lucide="chevron-right" class="w-4 h-4 text-muted-foreground"></i>
                </div>
            `;
        } else {
            html += `
                <div class="flex items-center justify-between text-sm py-1 cursor-pointer hover:text-primary"
                     onclick="showHolidayDetails(${holiday.id})" title="${holiday.description || ''}">
                    <span class="truncate">${holiday.name}</span>
                    <span class="text-xs text-muted-foreground ml-2">${date.getDate()}</span>
                </div>
            `;
        }
    });

    return html;
}

/**
 * Navigate calendar (prev/next/today)
 */
function navigateCalendar(direction) {
    if (direction === 'prev') {
        if (currentMonth === 0) {
            currentYear--;
        } else {
            currentMonth--;
            if (currentMonth === 0) {
                currentMonth = 12;
                currentYear--;
            }
        }
    } else if (direction === 'next') {
        if (currentMonth === 0) {
            currentYear++;
        } else {
            currentMonth++;
            if (currentMonth > 12) {
                currentMonth = 1;
                currentYear++;
            }
        }
    } else if (direction === 'today') {
        const today = new Date();
        currentYear = today.getFullYear();
        currentMonth = today.getMonth() + 1;
    }

    // Update selectors
    document.getElementById('yearSelect').value = currentYear;
    document.getElementById('monthSelect').value = currentMonth;

    // Update calendar
    updateCalendar();
}

/**
 * Refresh calendar
 */
function refreshCalendar() {
    updateCalendar();
}

/**
 * Update calendar title
 */
function updateCalendarTitle() {
    const title = document.getElementById('calendarTitle');
    if (title && currentCalendarData) {
        let titleText = `${currentCalendarData.year}`;

        if (currentMonth > 0) {
            const monthNames = [
                '', 'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];
            titleText += ` - ${monthNames[currentMonth]}`;
        }

        titleText += ` (${getRegionFlag(currentCalendarData.region_code)} ${getRegionName(currentCalendarData.region_code)})`;

        title.textContent = titleText;
    }
}

/**
 * Update upcoming holidays sidebar
 */
async function updateUpcomingHolidays() {
    try {
        const response = await fetch(`/api/holidays/upcoming/${currentRegion}?days=60`);
        const data = await response.json();

        if (data.success) {
            renderUpcomingHolidays(data.holidays);
        }
    } catch (error) {
        console.error('Error fetching upcoming holidays:', error);
    }
}

/**
 * Render upcoming holidays
 */
function renderUpcomingHolidays(holidays) {
    const container = document.getElementById('upcomingHolidays');

    if (holidays.length === 0) {
        container.innerHTML = '<p class="text-muted-foreground text-sm">No upcoming holidays</p>';
        return;
    }

    let html = '';
    holidays.slice(0, 5).forEach(holiday => {
        const date = new Date(holiday.date);
        const daysUntil = Math.ceil((date - new Date()) / (1000 * 60 * 60 * 24));

        html += `
            <div class="flex items-center justify-between text-sm">
                <div>
                    <div class="font-medium">${holiday.name}</div>
                    <div class="text-muted-foreground">${formatDate(date)}</div>
                </div>
                <div class="text-xs text-muted-foreground">
                    ${daysUntil > 0 ? `${daysUntil} days` : 'Today'}
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

/**
 * Update holiday statistics
 */
function updateHolidayStats() {
    const container = document.getElementById('holidayStats');

    if (!currentCalendarData) {
        container.innerHTML = '<p class="text-muted-foreground text-sm">Loading...</p>';
        return;
    }

    const totalHolidays = currentCalendarData.total_holidays;
    const holidaysByRegion = getHolidaysByRegion(currentCalendarData.holidays);

    let html = `
        <div class="flex items-center justify-between text-sm">
            <span>Total Holidays</span>
            <span class="font-medium">${totalHolidays}</span>
        </div>
    `;

    Object.entries(holidaysByRegion).forEach(([region, count]) => {
        html += `
            <div class="flex items-center justify-between text-sm">
                <span>${getRegionFlag(region)} ${getRegionName(region)}</span>
                <span class="font-medium">${count}</span>
            </div>
        `;
    });

    container.innerHTML = html;
}

/**
 * Get holidays grouped by region
 */
function getHolidaysByRegion(holidays) {
    const regionCounts = {};

    Object.values(holidays).forEach(holidayList => {
        holidayList.forEach(holiday => {
            regionCounts[holiday.region_code] = (regionCounts[holiday.region_code] || 0) + 1;
        });
    });

    return regionCounts;
}

/**
 * Show holiday details in drawer
 */
function showHolidayDetails(holidayId) {
    if (window.drawerManager) {
        window.drawerManager.openForm('holiday', holidayId);
    }
}





/**
 * Show calendar loading state
 */
function showCalendarLoading() {
    const container = document.getElementById('calendarContainer');
    container.innerHTML = `
        <div class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p class="text-muted-foreground mt-2">Loading calendar...</p>
        </div>
    `;
}

/**
 * Show calendar error
 */
function showCalendarError(message) {
    const container = document.getElementById('calendarContainer');
    container.innerHTML = `
        <div class="text-center py-8">
            <i data-lucide="alert-circle" class="w-8 h-8 text-destructive mx-auto mb-2"></i>
            <p class="text-destructive">${message}</p>
            <button onclick="updateCalendar()" class="btn btn-secondary mt-4">
                <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                Retry
            </button>
        </div>
    `;

    // Re-initialize Lucide icons
    if (window.lucide) {
        window.lucide.createIcons();
    }
}

/**
 * Utility functions
 */

function getRegionColor(regionCode) {
    const colors = {
        'US': 'bg-blue-500',
        'PH': 'bg-green-500',
        'GLOBAL': 'bg-gray-500'
    };
    return colors[regionCode] || 'bg-purple-500';
}

function getRegionFlag(regionCode) {
    const flags = {
        'US': '🇺🇸',
        'PH': '🇵🇭',
        'GLOBAL': '🌍'
    };
    return flags[regionCode] || '🏳️';
}

function getRegionName(regionCode) {
    const names = {
        'US': 'United States',
        'PH': 'Philippines',
        'GLOBAL': 'Global'
    };
    return names[regionCode] || regionCode;
}

function formatDate(date) {
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function isDateToday(date) {
    const today = new Date();
    return date.toDateString() === today.toDateString();
}



function showToast(message, type = 'info') {
    if (window.showToast) {
        window.showToast(message, type);
    } else {
        alert(message);
    }
}

// Handle selector changes
document.addEventListener('change', function(event) {
    if (event.target.id === 'regionSelect') {
        currentRegion = event.target.value;
        updateCalendar();
    } else if (event.target.id === 'yearSelect') {
        currentYear = parseInt(event.target.value);
        updateCalendar();
    } else if (event.target.id === 'monthSelect') {
        currentMonth = parseInt(event.target.value);
        updateCalendar();
    }
});

// Handle keyboard shortcuts
document.addEventListener('keydown', function(event) {
    if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
        event.preventDefault();
        if (window.drawerManager) {
            window.drawerManager.openForm('holiday');
        }
    }
});
